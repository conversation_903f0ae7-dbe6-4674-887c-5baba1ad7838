<!--
  微信小程序视频号演示页面
  展示不同场景下的视频号视频播放功能
-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-title">视频号视频播放演示</view>
  
  <!-- 功能说明 -->
  <view class="description">
    <text class="desc-title">功能说明：</text>
    <text class="desc-text">本页面演示微信小程序中视频号视频的两种播放方式</text>
  </view>

  <!-- 跳转播放方式 -->
  <view class="section">
    <view class="section-title">
      <text class="title-text">1. 跳转播放（无主体限制）</text>
      <text class="title-desc">通过wx.openChannelsActivity接口跳转到视频号观看</text>
    </view>
    
    <view class="demo-card">
      <view class="card-header">
        <text class="card-title">跳转播放示例</text>
      </view>
      <view class="card-content">
        <view class="input-group">
          <text class="label">视频号ID (finderUserName):</text>
          <input class="input" 
                 placeholder="请输入视频号ID" 
                 value="{{jumpParams.finderUserName}}"
                 bindinput="onFinderUserNameInput" />
        </view>
        <view class="input-group">
          <text class="label">视频ID (feedId):</text>
          <input class="input" 
                 placeholder="请输入视频ID" 
                 value="{{jumpParams.feedId}}"
                 bindinput="onFeedIdInput" />
        </view>
        <button class="demo-btn primary" bindtap="openChannelsVideo">
          跳转播放视频
        </button>
      </view>
      <view class="card-note">
        <text class="note-text">
          • 无主体限制，任何小程序都可以使用
          • 会跳转到微信视频号页面观看
          • 用户体验：离开小程序 → 观看视频 → 返回小程序
        </text>
      </view>
    </view>
  </view>

  <!-- 内嵌播放方式 -->
  <view class="section">
    <view class="section-title">
      <text class="title-text">2. 内嵌播放（有主体限制）</text>
      <text class="title-desc">通过channel-video组件在小程序内播放</text>
    </view>

    <!-- 同主体视频内嵌 -->
    <view class="demo-card">
      <view class="card-header">
        <text class="card-title">同主体视频内嵌</text>
        <text class="card-subtitle">（基础库2.25.1+）</text>
      </view>
      <view class="card-content">
        <view class="input-group">
          <text class="label">视频号ID:</text>
          <input class="input" 
                 placeholder="同主体视频号ID" 
                 value="{{embedParams.sameSubject.finderUserName}}"
                 bindinput="onSameSubjectFinderUserNameInput" />
        </view>
        <view class="input-group">
          <text class="label">视频ID:</text>
          <input class="input" 
                 placeholder="同主体视频ID" 
                 value="{{embedParams.sameSubject.feedId}}"
                 bindinput="onSameSubjectFeedIdInput" />
        </view>
        
        <!-- 同主体视频播放器 -->
        <view class="video-container" wx:if="{{embedParams.sameSubject.finderUserName && embedParams.sameSubject.feedId}}">
          <channel-video 
            finder-user-name="{{embedParams.sameSubject.finderUserName}}"
            feed-id="{{embedParams.sameSubject.feedId}}"
            bindload="onSameSubjectVideoLoad"
            binderror="onSameSubjectVideoError">
          </channel-video>
        </view>
        
        <button class="demo-btn secondary" bindtap="loadSameSubjectVideo">
          加载同主体视频
        </button>
      </view>
      <view class="card-note">
        <text class="note-text">
          • 要求：小程序与视频号为同主体或关联主体
          • 用户体验：在小程序内直接播放，无需跳转
          • 支持无弹窗跳转到视频号对应视频
        </text>
      </view>
    </view>

    <!-- 非同主体视频内嵌 -->
    <view class="demo-card">
      <view class="card-header">
        <text class="card-title">非同主体视频内嵌</text>
        <text class="card-subtitle">（基础库2.31.1+，非个人主体小程序）</text>
      </view>
      <view class="card-content">
        <view class="input-group">
          <text class="label">Feed Token:</text>
          <input class="input" 
                 placeholder="非同主体视频feed-token" 
                 value="{{embedParams.differentSubject.feedToken}}"
                 bindinput="onFeedTokenInput" />
        </view>
        
        <!-- 非同主体视频播放器 -->
        <view class="video-container" wx:if="{{embedParams.differentSubject.feedToken}}">
          <channel-video 
            feed-token="{{embedParams.differentSubject.feedToken}}"
            bindload="onDifferentSubjectVideoLoad"
            binderror="onDifferentSubjectVideoError">
          </channel-video>
        </view>
        
        <button class="demo-btn secondary" bindtap="loadDifferentSubjectVideo">
          加载非同主体视频
        </button>
      </view>
      <view class="card-note">
        <text class="note-text">
          • 要求：非个人主体小程序
          • 需要在MP平台开启"获取视频号视频ID权限"
          • 24小时时效限制，需要重新获取feed-token
        </text>
      </view>
    </view>
  </view>

  <!-- 错误信息显示 -->
  <view class="error-section" wx:if="{{errorMessage}}">
    <view class="error-title">错误信息：</view>
    <view class="error-content">{{errorMessage}}</view>
  </view>

  <!-- 技术对比说明 -->
  <view class="comparison-section">
    <view class="section-title">
      <text class="title-text">技术对比总结</text>
    </view>
    <view class="comparison-table">
      <view class="table-header">
        <view class="col-1">对比项</view>
        <view class="col-2">跳转播放</view>
        <view class="col-3">内嵌播放</view>
      </view>
      <view class="table-row">
        <view class="col-1">主体限制</view>
        <view class="col-2">无限制</view>
        <view class="col-3">有限制</view>
      </view>
      <view class="table-row">
        <view class="col-1">用户体验</view>
        <view class="col-2">需跳转</view>
        <view class="col-3">无缝播放</view>
      </view>
      <view class="table-row">
        <view class="col-1">基础库要求</view>
        <view class="col-2">2.19.2+</view>
        <view class="col-3">2.25.1+</view>
      </view>
      <view class="table-row">
        <view class="col-1">权限控制</view>
        <view class="col-2">简单</view>
        <view class="col-3">复杂</view>
      </view>
    </view>
  </view>
</view>
