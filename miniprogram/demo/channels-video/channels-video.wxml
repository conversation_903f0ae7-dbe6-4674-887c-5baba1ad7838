<!-- 微信小程序视频号演示页面
  展示不同场景下的视频号视频播放功能 -->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-title">视频号视频播放演示</view>
  <!-- 功能说明 -->
  <view class="description">
    <text class="desc-title">功能说明：</text>
    <text class="desc-text">本页面演示微信小程序中视频号视频的两种播放方式</text>
  </view>
  <!-- 跳转播放方式 -->
  <view class="section">
    <view class="section-title">
      <text class="title-text">1. 跳转播放（无主体限制）</text>
      <text class="title-desc">通过wx.openChannelsActivity接口跳转到视频号观看</text>
    </view>
    <view class="demo-card">
      <view class="card-header">
        <text class="card-title">跳转播放示例</text>
      </view>
      <view class="card-content">
        <view class="input-group">
          <text class="label">视频号ID (finderUserName):</text>
          <input class="input" placeholder="请输入视频号ID" value="{{jumpParams.finderUserName}}" bindinput="onFinderUserNameInput" />
        </view>
        <view class="input-group">
          <text class="label">视频ID (feedId):</text>
          <input class="input" placeholder="请输入视频ID" value="{{jumpParams.feedId}}" bindinput="onFeedIdInput" />
        </view>
        <button class="demo-btn primary" bindtap="openChannelsVideo">跳转播放视频</button>
      </view>
      <view class="card-note">
        <text class="note-text">
          • 无主体限制，任何小程序都可以使用
          • 会跳转到微信视频号页面观看
          • 用户体验：离开小程序 → 观看视频 → 返回小程序
        </text>
      </view>
    </view>
  </view>
  <!-- 内嵌播放方式 -->
  <view class="section">
    <view class="section-title">
      <text class="title-text">2. 内嵌播放（有主体限制）</text>
      <text class="title-desc">通过channel-video组件在小程序内播放</text>
    </view>
    <!-- 同主体视频内嵌 -->
    <view class="demo-card">
      <view class="card-header">
        <text class="card-title">同主体视频内嵌</text>
        <text class="card-subtitle">（基础库2.25.1+）</text>
      </view>
      <view class="card-content">
        <view class="input-group">
          <text class="label">视频号ID:</text>
          <input class="input" placeholder="同主体视频号ID" value="{{embedParams.sameSubject.finderUserName}}" bindinput="onSameSubjectFinderUserNameInput" />
        </view>
        <view class="input-group">
          <text class="label">视频ID:</text>
          <input class="input" placeholder="同主体视频ID" value="{{embedParams.sameSubject.feedId}}" bindinput="onSameSubjectFeedIdInput" />
        </view>
        <!-- 播放控制选项 -->
        <view class="control-options">
          <view class="option-row">
            <text class="option-label">自动播放:</text>
            <switch checked="{{embedParams.sameSubject.autoplay}}" bindchange="onAutoplayChange" />
          </view>
          <view class="option-row">
            <text class="option-label">循环播放:</text>
            <switch checked="{{embedParams.sameSubject.loop}}" bindchange="onLoopChange" />
          </view>
          <view class="option-row">
            <text class="option-label">静音播放:</text>
            <switch checked="{{embedParams.sameSubject.muted}}" bindchange="onMutedChange" />
          </view>
          <view class="option-row">
            <text class="option-label">显示模式:</text>
            <picker bindchange="onObjectFitChange" value="{{embedParams.sameSubject.objectFitIndex}}" range="{{objectFitOptions}}" range-key="label">
              <view class="picker">
                {{objectFitOptions[embedParams.sameSubject.objectFitIndex].label}}
              </view>
            </picker>
          </view>
        </view>
        <!-- 同主体视频播放器 -->
        <view class="video-container" wx:if="{{embedParams.sameSubject.finderUserName && embedParams.sameSubject.feedId}}">
          <channel-video finder-user-name="{{embedParams.sameSubject.finderUserName}}" feed-id="{{embedParams.sameSubject.feedId}}" autoplay="{{embedParams.sameSubject.autoplay}}" loop="{{embedParams.sameSubject.loop}}" muted="{{embedParams.sameSubject.muted}}" object-fit="{{objectFitOptions[embedParams.sameSubject.objectFitIndex].value}}" bindload="onSameSubjectVideoLoad" binderror="onSameSubjectVideoError"></channel-video>
        </view>
        <button class="demo-btn secondary" bindtap="loadSameSubjectVideo">加载同主体视频</button>
      </view>
      <view class="card-note">
        <text class="note-text">
          • 要求：小程序与视频号为同主体或关联主体
          • 用户体验：在小程序内直接播放，无需跳转
          • 支持无弹窗跳转到视频号对应视频
        </text>
      </view>
    </view>
    <!-- 非同主体视频内嵌 -->
    <view class="demo-card">
      <view class="card-header">
        <text class="card-title">非同主体视频内嵌</text>
        <text class="card-subtitle">（基础库2.31.1+，非个人主体小程序）</text>
      </view>
      <view class="card-content">
        <view class="input-group">
          <text class="label">Feed Token:</text>
          <input class="input" placeholder="非同主体视频feed-token" value="{{embedParams.differentSubject.feedToken}}" bindinput="onFeedTokenInput" />
        </view>
        <!-- 非同主体视频播放器 -->
        <view class="video-container" wx:if="{{embedParams.differentSubject.feedToken}}">
          <channel-video feed-token="{{embedParams.differentSubject.feedToken}}" bindload="onDifferentSubjectVideoLoad" binderror="onDifferentSubjectVideoError"></channel-video>
        </view>
        <button class="demo-btn secondary" bindtap="loadDifferentSubjectVideo">加载非同主体视频</button>
      </view>
      <view class="card-note">
        <text class="note-text">
          • 要求：非个人主体小程序
          • 需要在MP平台开启"获取视频号视频ID权限"
          • 24小时时效限制，需要重新获取feed-token
        </text>
      </view>
    </view>
  </view>
  <!-- 错误信息显示 -->
  <view class="error-section" wx:if="{{errorMessage}}">
    <view class="error-title">错误信息：</view>
    <view class="error-content">{{errorMessage}}</view>
  </view>
  <!-- 技术误解澄清 -->
  <view class="clarification-section">
    <view class="section-title">
      <text class="title-text">⚠️ 常见技术误解澄清</text>
    </view>
    <view class="clarification-card">
      <view class="clarification-header">
        <text class="clarification-title">错误方案：声称wx.openChannelsActivity可以"无缝嵌入播放"</text>
      </view>
      <view class="clarification-content">
        <view class="myth-item">
          <text class="myth-label">❌ 错误声明：</text>
          <text class="myth-text">"从屏幕底部弹出定制播放器，用户无法上下滑动"</text>
        </view>
        <view class="fact-item">
          <text class="fact-label">✅ 实际情况：</text>
          <text class="fact-text">wx.openChannelsActivity是完整跳转到视频号，用户可以自由滑动查看其他视频</text>
        </view>
        <view class="solution-item">
          <text class="solution-label">🎯 正确方案：</text>
          <text class="solution-text">要避免注意力分散，应使用channel-video组件进行内嵌播放</text>
        </view>
      </view>
    </view>
  </view>
  <!-- channel-video功能特性分析 -->
  <view class="feature-analysis-section">
    <view class="section-title">
      <text class="title-text">📊 channel-video组件功能特性分析</text>
    </view>
    <view class="feature-grid">
      <!-- 播放控制功能 -->
      <view class="feature-card">
        <view class="feature-header">
          <text class="feature-title">🎮 播放控制功能</text>
        </view>
        <view class="feature-content">
          <view class="feature-item">
            <text class="feature-label">播放/暂停:</text>
            <text class="feature-status supported">✅ 支持</text>
          </view>
          <view class="feature-item">
            <text class="feature-label">自动播放:</text>
            <text class="feature-status limited">⚠️ 仅同主体</text>
          </view>
          <view class="feature-item">
            <text class="feature-label">循环播放:</text>
            <text class="feature-status supported">✅ 支持</text>
          </view>
          <view class="feature-item">
            <text class="feature-label">静音播放:</text>
            <text class="feature-status supported">✅ 支持</text>
          </view>
          <view class="feature-item">
            <text class="feature-label">进度控制:</text>
            <text class="feature-status unknown">❓ 需测试</text>
          </view>
        </view>
      </view>
      <!-- 显示模式 -->
      <view class="feature-card">
        <view class="feature-header">
          <text class="feature-title">📺 显示模式</text>
        </view>
        <view class="feature-content">
          <view class="feature-item">
            <text class="feature-label">包含模式:</text>
            <text class="feature-status supported">✅ contain</text>
          </view>
          <view class="feature-item">
            <text class="feature-label">填充模式:</text>
            <text class="feature-status supported">✅ fill</text>
          </view>
          <view class="feature-item">
            <text class="feature-label">覆盖模式:</text>
            <text class="feature-status supported">✅ cover</text>
          </view>
          <view class="feature-item">
            <text class="feature-label">全屏播放:</text>
            <text class="feature-status unknown">❓ 需测试</text>
          </view>
          <view class="feature-item">
            <text class="feature-label">画中画:</text>
            <text class="feature-status unknown">❓ 需测试</text>
          </view>
        </view>
      </view>
      <!-- 社交互动功能 -->
      <view class="feature-card">
        <view class="feature-header">
          <text class="feature-title">💬 社交互动功能</text>
        </view>
        <view class="feature-content">
          <view class="feature-item">
            <text class="feature-label">点赞功能:</text>
            <text class="feature-status not-supported">❌ 不支持</text>
          </view>
          <view class="feature-item">
            <text class="feature-label">转发分享:</text>
            <text class="feature-status not-supported">❌ 不支持</text>
          </view>
          <view class="feature-item">
            <text class="feature-label">评论功能:</text>
            <text class="feature-status not-supported">❌ 不支持</text>
          </view>
          <view class="feature-item">
            <text class="feature-label">关注主播:</text>
            <text class="feature-status not-supported">❌ 不支持</text>
          </view>
          <view class="feature-item">
            <text class="feature-label">跳转视频号:</text>
            <text class="feature-status supported">✅ 无弹窗跳转</text>
          </view>
        </view>
      </view>
      <!-- 技术限制 -->
      <view class="feature-card">
        <view class="feature-header">
          <text class="feature-title">⚠️ 技术限制</text>
        </view>
        <view class="feature-content">
          <view class="feature-item">
            <text class="feature-label">主体限制:</text>
            <text class="feature-status limited">⚠️ 严格限制</text>
          </view>
          <view class="feature-item">
            <text class="feature-label">基础库要求:</text>
            <text class="feature-status limited">⚠️ 2.25.1+</text>
          </view>
          <view class="feature-item">
            <text class="feature-label">图片视频:</text>
            <text class="feature-status not-supported">❌ 不支持</text>
          </view>
          <view class="feature-item">
            <text class="feature-label">非同主体自动播放:</text>
            <text class="feature-status not-supported">❌ 强制禁用</text>
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 技术对比说明 -->
  <view class="comparison-section">
    <view class="section-title">
      <text class="title-text">技术对比总结</text>
    </view>
    <view class="comparison-table">
      <view class="table-header">
        <view class="col-1">对比项</view>
        <view class="col-2">跳转播放</view>
        <view class="col-3">内嵌播放</view>
      </view>
      <view class="table-row">
        <view class="col-1">主体限制</view>
        <view class="col-2">无限制</view>
        <view class="col-3">有限制</view>
      </view>
      <view class="table-row">
        <view class="col-1">用户体验</view>
        <view class="col-2">需跳转</view>
        <view class="col-3">无缝播放</view>
      </view>
      <view class="table-row highlight-row">
        <view class="col-1">注意力分散</view>
        <view class="col-2">❌ 可滑动看其他视频</view>
        <view class="col-3">✅ 专注当前视频</view>
      </view>
      <view class="table-row">
        <view class="col-1">播放环境</view>
        <view class="col-2">完整视频号界面</view>
        <view class="col-3">小程序内播放器</view>
      </view>
      <view class="table-row">
        <view class="col-1">基础库要求</view>
        <view class="col-2">2.19.2+</view>
        <view class="col-3">2.25.1+</view>
      </view>
      <view class="table-row">
        <view class="col-1">权限控制</view>
        <view class="col-2">简单</view>
        <view class="col-3">复杂</view>
      </view>
    </view>
  </view>
</view>